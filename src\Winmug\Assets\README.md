# MSIX Package Assets

This folder contains the required image assets for the WinMug MSIX package. You need to create the following images based on your WinMugLogo.png:

## Required Assets

### App Icons
- **Square44x44Logo.png** - 44x44 pixels
  - Used for: App list, search results, taskbar
  - Format: PNG with transparency support
  - Background: Transparent or app theme color

- **Square150x150Logo.png** - 150x150 pixels  
  - Used for: Start menu tile (medium)
  - Format: PNG with transparency support
  - Background: Transparent or app theme color

### Tiles
- **Wide310x150Logo.png** - 310x150 pixels
  - Used for: Start menu tile (wide)
  - Format: PNG with transparency support
  - Background: Transparent or app theme color

### Store Assets
- **StoreLogo.png** - 50x50 pixels
  - Used for: Microsoft Store listing
  - Format: PNG with transparency support
  - Background: Transparent

### Splash Screen
- **SplashScreen.png** - 620x300 pixels
  - Used for: App startup screen
  - Format: PNG
  - Background: Can be solid color or transparent

## Creating Assets from WinMugLogo.png

You can use the existing `src/Winmug/Images/WinMugLogo.png` as a base and resize it to create these assets. 

### Recommended Tools:
- **GIMP** (Free) - Good for batch resizing
- **Paint.NET** (Free) - Simple resizing with plugins
- **Adobe Photoshop** - Professional option
- **Online tools** - Like Canva or Figma

### Tips:
1. Maintain aspect ratio when possible
2. Use transparent backgrounds for better integration
3. Ensure icons are clear and readable at small sizes
4. Test on different Windows themes (light/dark)
5. Consider adding padding for better visual balance

## Alternative: Auto-generated Assets

Visual Studio can auto-generate some assets from a single source image:
1. Right-click project in Visual Studio
2. Add > New Item > Visual Assets
3. Upload your source logo
4. Let Visual Studio generate the required sizes

## Validation

After creating assets, validate them using:
- Windows App Certification Kit
- Visual Studio MSIX packaging tools
- Manual testing on different screen DPI settings
