# WinMug MSIX Packaging Guide

This guide explains how to package WinMug as an MSIX for Microsoft Store distribution.

## Prerequisites

1. **Visual Studio 2022** with:
   - .NET 8.0 SDK
   - Windows Application Packaging Project template
   - MSIX Packaging Tools

2. **Windows 10/11** version 1809 or later

3. **Developer Certificate** for signing (or Microsoft Store account for store submission)

## Files Created

- `AppxManifest.xml` - Main manifest file for MSIX packaging
- `Package.appxmanifest` - Alternative manifest format
- `Assets/` - Directory for required image assets
- `Assets/README.md` - Guide for creating required images

## Step-by-Step Packaging Process

### 1. Create Required Assets

Before packaging, create the required image assets in the `Assets/` folder:

```
Assets/
├── Square44x44Logo.png      (44x44)
├── Square150x150Logo.png    (150x150)  
├── Wide310x150Logo.png      (310x150)
├── StoreLogo.png            (50x50)
└── SplashScreen.png         (620x300)
```

Use `src/Winmug/Images/WinMugLogo.png` as the source and resize accordingly.

### 2. Option A: Using Visual Studio Packaging Project

1. **Add Windows Application Packaging Project:**
   ```
   File > Add > New Project > Windows Application Packaging Project
   ```

2. **Configure the project:**
   - Name: `WinMug.Package`
   - Target Version: Windows 10, version 1903 (10.0; Build 18362)
   - Minimum Version: Windows 10, version 1809 (10.0; Build 17763)

3. **Add WinMug as dependency:**
   - Right-click `Applications` in packaging project
   - Add Reference > Projects > Select `Winmug`

4. **Copy manifest:**
   - Replace the generated `Package.appxmanifest` with our `Package.appxmanifest`
   - Copy `Assets/` folder to the packaging project

5. **Build and create package:**
   ```
   Build > Batch Build > Select Release configuration
   Project > Store > Create App Packages
   ```

### 3. Option B: Using Command Line Tools

1. **Build the application:**
   ```powershell
   dotnet publish src/Winmug -c Release -r win-x64 --self-contained false
   ```

2. **Use MakeAppx tool:**
   ```powershell
   # Create package
   MakeAppx.exe pack /d "src\Winmug\bin\Release\net8.0-windows\win-x64\publish" /p "WinMug.msix" /l

   # Sign package (for testing)
   SignTool.exe sign /fd SHA256 /a /f "YourCertificate.pfx" /p "password" "WinMug.msix"
   ```

### 4. Option C: Using MSIX Packaging Tool

1. **Download MSIX Packaging Tool** from Microsoft Store
2. **Create package from installer:**
   - Launch MSIX Packaging Tool
   - Select "Application package"
   - Follow wizard to capture installation
   - Use our `AppxManifest.xml` as template

## Manifest Configuration Details

### Publisher Information
- **Publisher:** `CN=YVCode Apps`
- **PublisherDisplayName:** `YVCode Apps`
- **Package Name:** `YVCodeApps.WinMug`

### Capabilities
- **internetClient:** Required for SmugMug API access
- **runFullTrust:** Required for .NET desktop applications

### Target Frameworks
- **Minimum:** Windows 10 version 1809 (Build 17763)
- **Tested:** Windows 10 version 2004 (Build 19041)

## Testing the Package

### 1. Local Installation
```powershell
# Install for testing
Add-AppxPackage -Path "WinMug.msix"

# Uninstall
Get-AppxPackage *WinMug* | Remove-AppxPackage
```

### 2. Windows App Certification Kit
```powershell
# Run certification tests
"C:\Program Files (x86)\Windows Kits\10\App Certification Kit\appcert.exe" test -appxpackagepath "WinMug.msix"
```

## Microsoft Store Submission

### 1. Prepare Store Assets
- App screenshots (1366x768, 1920x1080)
- Store logos (300x300, 150x150)
- App description and features list
- Privacy policy URL
- Support contact information

### 2. Partner Center Setup
1. Create Microsoft Partner Center account
2. Reserve app name: "WinMug - SmugMug Photo Downloader"
3. Set pricing: $7.99 (as planned)
4. Configure age ratings and content declarations

### 3. Upload Package
1. Upload the signed MSIX package
2. Complete store listing information
3. Submit for certification

## Troubleshooting

### Common Issues:
1. **Missing assets:** Ensure all required images are in Assets/ folder
2. **Certificate errors:** Use proper code signing certificate
3. **Capability issues:** Verify runFullTrust capability for .NET apps
4. **Version conflicts:** Ensure version numbers are incremented for updates

### Validation Tools:
- Windows App Certification Kit
- Visual Studio App Package validation
- MSIX Packaging Tool validation

## Version Management

For updates:
1. Increment version in manifest (e.g., *******)
2. Rebuild and repackage
3. Submit update through Partner Center

## Support Resources

- [MSIX Documentation](https://docs.microsoft.com/en-us/windows/msix/)
- [Microsoft Store Policies](https://docs.microsoft.com/en-us/windows/uwp/publish/store-policies)
- [Partner Center Guide](https://docs.microsoft.com/en-us/windows/uwp/publish/)
