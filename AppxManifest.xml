<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
         xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
         IgnorableNamespaces="uap rescap">

  <Identity Name="YVCodeApps.WinMug"
            Publisher="CN=YVCode Apps"
            Version="*******" />

  <mp:PhoneIdentity PhoneProductId="********-1234-1234-1234-************" PhonePublisherId="********-0000-0000-0000-************"/>

  <Properties>
    <DisplayName>WinMug - SmugMug Photo Downloader</DisplayName>
    <PublisherDisplayName>YVCode Apps</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
    <Description>Download your entire SmugMug photo library to your local computer with WinMug. Easily browse, select, and download albums and photos from your SmugMug account while preserving folder structure and metadata.</Description>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
  </Dependencies>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
      Executable="winmug.exe"
      EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements
        DisplayName="WinMug"
        Description="SmugMug Photo Downloader - Download your entire SmugMug photo library"
        BackgroundColor="transparent"
        Square150x150Logo="Assets\Square150x150Logo.png"
        Square44x44Logo="Assets\Square44x44Logo.png">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" />
        <uap:SplashScreen Image="Assets\SplashScreen.png" />
      </uap:VisualElements>
    </Application>
  </Applications>

  <Capabilities>
    <Capability Name="internetClient" />
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
</Package>

