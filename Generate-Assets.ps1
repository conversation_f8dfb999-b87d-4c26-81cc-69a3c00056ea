# PowerShell script to generate MSIX assets from WinMugLogo.png
# Requires .NET Framework or .NET Core with System.Drawing.Common

param(
    [string]$SourceImage = "src\Winmug\Images\WinMugLogo.png",
    [string]$OutputDir = "Assets"
)

# Load required assemblies
Add-Type -AssemblyName System.Drawing

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force
}

# Function to resize image
function Resize-Image {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Width,
        [int]$Height
    )
    
    try {
        # Load source image
        $sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $InputPath).Path)
        
        # Create new bitmap with target size
        $targetBitmap = New-Object System.Drawing.Bitmap($Width, $Height)
        
        # Create graphics object for drawing
        $graphics = [System.Drawing.Graphics]::FromImage($targetBitmap)
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        # Calculate scaling to maintain aspect ratio
        $sourceWidth = $sourceImage.Width
        $sourceHeight = $sourceImage.Height
        $scaleX = $Width / $sourceWidth
        $scaleY = $Height / $sourceHeight
        $scale = [Math]::Min($scaleX, $scaleY)
        
        # Calculate centered position
        $scaledWidth = [int]($sourceWidth * $scale)
        $scaledHeight = [int]($sourceHeight * $scale)
        $x = [int](($Width - $scaledWidth) / 2)
        $y = [int](($Height - $scaledHeight) / 2)
        
        # Draw resized image
        $graphics.DrawImage($sourceImage, $x, $y, $scaledWidth, $scaledHeight)
        
        # Save as PNG
        $targetBitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Cleanup
        $graphics.Dispose()
        $targetBitmap.Dispose()
        $sourceImage.Dispose()
        
        Write-Host "Created: $OutputPath ($Width x $Height)" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create $OutputPath : $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Check if source image exists
if (!(Test-Path $SourceImage)) {
    Write-Host "Error: Source image not found at $SourceImage" -ForegroundColor Red
    exit 1
}

Write-Host "Generating MSIX assets from $SourceImage..." -ForegroundColor Cyan
Write-Host ""

# Define required assets with their dimensions
$assets = @(
    @{ Name = "Square44x44Logo.png"; Width = 44; Height = 44; Description = "App list, search results, taskbar" },
    @{ Name = "Square150x150Logo.png"; Width = 150; Height = 150; Description = "Start menu tile (medium)" },
    @{ Name = "Wide310x150Logo.png"; Width = 310; Height = 150; Description = "Start menu tile (wide)" },
    @{ Name = "StoreLogo.png"; Width = 50; Height = 50; Description = "Microsoft Store listing" },
    @{ Name = "SplashScreen.png"; Width = 620; Height = 300; Description = "App startup screen" }
)

# Generate each asset
foreach ($asset in $assets) {
    $outputPath = Join-Path $OutputDir $asset.Name
    Write-Host "Generating $($asset.Name) - $($asset.Description)"
    Resize-Image -InputPath $SourceImage -OutputPath $outputPath -Width $asset.Width -Height $asset.Height
}

Write-Host ""
Write-Host "Asset generation complete!" -ForegroundColor Green
Write-Host "Assets created in: $OutputDir" -ForegroundColor Yellow

# List generated files
Write-Host ""
Write-Host "Generated files:" -ForegroundColor Cyan
Get-ChildItem $OutputDir -Filter "*.png" | ForEach-Object {
    $size = [System.Drawing.Image]::FromFile($_.FullName)
    Write-Host "  $($_.Name) - $($size.Width)x$($size.Height)" -ForegroundColor Gray
    $size.Dispose()
}
