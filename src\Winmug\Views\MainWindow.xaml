<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Winmug.Converters"
        xmlns:models="clr-namespace:Winmug.Core.Models;assembly=Winmug.Core"
        mc:Ignorable="d"
        Title="WinMug - SmugMug Photo Downloader"
        Height="700" Width="1000"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource DarkBackground}"
        SizeToContent="Height">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Theme Toggle -->
        <Grid Grid.Row="0" Margin="0,0,0,30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Title Section -->
            <StackPanel Grid.Column="0" Background="Transparent">
                <!-- WinMug Logo -->
                <Image Source="/Images/WinMugLogo.png"
                       Width="260"
                       Height="140"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,0"
                       Stretch="Uniform"
                       RenderOptions.BitmapScalingMode="HighQuality"
                       SnapsToDevicePixels="True"
                       UseLayoutRounding="True"/>
                <TextBlock Text="Download your entire SmugMug photo library to your local computer"
                           FontSize="16" Foreground="{DynamicResource SecondaryText}"
                           HorizontalAlignment="Center" Margin="0,-30,0,0"/>
            </StackPanel>

            <!-- Theme Toggle Button -->
            <Button Grid.Column="1" VerticalAlignment="Top" HorizontalAlignment="Right"
                    Command="{Binding ToggleThemeCommand}"
                    Width="40" Height="40" Padding="8"
                    Background="{DynamicResource CardBackground}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="1"
                    ToolTip="{Binding IsDarkTheme, Converter={x:Static local:ThemeTooltipConverter.Instance}}">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="20">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Button.Style>
                <TextBlock Text="{Binding IsDarkTheme, Converter={x:Static local:ThemeIconConverter.Instance}}"
                           FontSize="16" Foreground="{DynamicResource PrimaryText}"/>
            </Button>
        </Grid>

        <!-- Authentication Section with Dynamic Theme -->
        <Border Grid.Row="1" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Profile Section (when authenticated) -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <!-- Profile Picture with SmugMug Image or Initials -->
                        <Border Width="64" Height="64" CornerRadius="32" Background="{DynamicResource PrimaryBlue}" Margin="0,0,20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#1E88E5" Direction="270" ShadowDepth="1" BlurRadius="4" Opacity="0.3"/>
                            </Border.Effect>
                            <Grid>
                                <!-- SmugMug Profile Image (if available) -->
                                <Image Source="{Binding UserProfileImageUrl}"
                                       Width="64" Height="64"
                                       Stretch="UniformToFill"
                                       Visibility="{Binding HasProfileImage, Converter={StaticResource BooleanToVisibilityConverter}}"
                                       RenderOptions.BitmapScalingMode="HighQuality">
                                    <Image.Clip>
                                        <EllipseGeometry Center="32,32" RadiusX="32" RadiusY="32"/>
                                    </Image.Clip>
                                </Image>
                                <!-- Fallback Initials (if no profile image) -->
                                <TextBlock Text="{Binding UserDisplayName, Converter={x:Static local:FirstLetterConverter.Instance}}"
                                           FontSize="24" FontWeight="Bold" Foreground="White"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Visibility="{Binding HasProfileImage, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                            </Grid>
                        </Border>

                        <!-- User Info and Action Buttons -->
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="{Binding UserDisplayName}" FontSize="20" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                            <TextBlock Text="{Binding UserStatusText}" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="0,4,0,12"/>

                            <!-- Action Buttons with Dynamic Theme -->
                            <StackPanel Orientation="Horizontal">
                                <Button Content="Show my albums"
                                        Command="{Binding LoadFolderStructureCommand}"
                                        Margin="0,0,12,0" Padding="16,8" FontSize="14" FontWeight="SemiBold"
                                        Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                <Button Content="Logout"
                                        Command="{Binding LogoutCommand}"
                                        Padding="16,8" FontSize="14" FontWeight="SemiBold"
                                        Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <!-- Authentication Prompt (when not authenticated) -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center"
                                Visibility="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Text="Please authenticate to access your SmugMug photos"
                                   FontSize="18" Foreground="{DynamicResource SecondaryText}" HorizontalAlignment="Center" Margin="0,0,0,16"/>
                        <Button Content="Authenticate with SmugMug"
                                Command="{Binding InitiateAuthenticationCommand}"
                                Padding="24,12" FontSize="16" FontWeight="SemiBold"
                                Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <Border.Effect>
                                                        <DropShadowEffect Color="#1E88E5" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                                    </Border.Effect>
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- Verification Code Input (fallback - only visible when automatic authentication fails) -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="Fallback: Enter verification code:" VerticalAlignment="Center" Margin="0,0,12,0" FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="140" Margin="0,0,12,0" Padding="10,8" FontSize="14"
                             BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Background="{DynamicResource CardBackground}" Foreground="{DynamicResource PrimaryText}"/>
                    <Button Content="Submit"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="16,8" Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0" FontSize="14" FontWeight="SemiBold">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- Progress Section with Green Dark Theme -->
                <Grid Margin="0,20,0,0"
                      Visibility="{Binding IsLoadingFolderStructure, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Status Message -->
                    <TextBlock Grid.Row="0" Text="{Binding StatusMessage}"
                               FontWeight="SemiBold" Margin="0,0,0,8" HorizontalAlignment="Center" FontSize="14" Foreground="{DynamicResource PrimaryText}"/>

                    <!-- Progress Bar with Blue -->
                    <ProgressBar Grid.Row="1" Value="{Binding AlbumsFoundCount}"
                                 Maximum="{Binding AlbumsFoundMaximum}"
                                 Height="8" Margin="0,0,0,6"
                                 Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}"/>

                    <!-- Progress Text -->
                    <TextBlock Grid.Row="2" Text="{Binding AlbumDiscoveryProgress}"
                               HorizontalAlignment="Center" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Albums Section with Dynamic Theme -->
        <Border Grid.Row="2" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Margin="0,0,0,16"
                Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Albums Header with Center-Aligned Controls -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                    <TextBlock Text="Albums" FontSize="20" FontWeight="SemiBold" Margin="0,0,24,0" VerticalAlignment="Center" Foreground="{DynamicResource PrimaryText}"/>

                    <!-- View Toggle Button -->
                    <Button Content="{Binding UseTreeView, Converter={x:Static local:TreeViewToggleConverter.Instance}}"
                            Command="{Binding ToggleTreeViewCommand}"
                            Padding="12,6" Margin="0,0,12,0" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}"
                            ToolTip="{Binding UseTreeView, Converter={x:Static local:TreeViewTooltipConverter.Instance}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="Select all" Command="{Binding SelectAllAlbumsCommand}"
                            Padding="12,6" Margin="0,0,12,0" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource PrimaryBlue}" BorderThickness="1" BorderBrush="{DynamicResource PrimaryBlue}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="Deselect all" Command="{Binding DeselectAllAlbumsCommand}"
                            Padding="12,6" FontSize="14" FontWeight="SemiBold" Margin="0,0,12,0"
                            Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    <!-- Tree View Controls (only visible when using tree view) -->
                    <Button Content="📂 Expand All" Command="{Binding ExpandAllFoldersCommand}"
                            Padding="12,6" Margin="0,0,12,0" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}"
                            Visibility="{Binding UseTreeView, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="📁 Collapse All" Command="{Binding CollapseAllFoldersCommand}"
                            Padding="12,6" Margin="0,0,12,0" FontSize="14" FontWeight="SemiBold"
                            Background="Transparent" Foreground="{DynamicResource SecondaryText}" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}"
                            Visibility="{Binding UseTreeView, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="🔍 Debug" Command="{Binding LogCurrentButtonStateCommand}"
                            Padding="8,6" FontSize="12" FontWeight="SemiBold"
                            Background="Transparent" Foreground="Orange" BorderThickness="1" BorderBrush="Orange"
                            Visibility="Collapsed">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- Album Count Info -->
                <StackPanel Grid.Row="1" Orientation="Vertical" HorizontalAlignment="Center" Margin="0,0,0,16">
                    <!-- Total Albums -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,4">
                        <TextBlock Text="Total albums found: " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding TotalAlbumCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                        <TextBlock Text=" (" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="6,0,0,0"/>
                        <TextBlock Text="{Binding FolderStructure.TotalEstimatedSize, Mode=OneWay}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                        <TextBlock Text=")" FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                    </StackPanel>

                    <!-- Selected Albums -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="Selected for download: " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding SelectedAlbumCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=" albums (" FontSize="14" Foreground="{DynamicResource SecondaryText}" Margin="4,0,0,0"/>
                        <TextBlock Text="{Binding TotalSelectedImageCount}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=" images, " FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding TotalSelectedSize}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                        <TextBlock Text=")" FontSize="14" Foreground="{DynamicResource SecondaryText}"/>
                    </StackPanel>
                </StackPanel>

                <!-- Albums View Container -->
                <Border Grid.Row="2" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="6" Background="{DynamicResource DarkBackground}" Height="400">
                    <Grid Margin="4">
                        <!-- Tree View (when UseTreeView is true) -->
                        <TreeView ItemsSource="{Binding AlbumTreeItems}"
                                  ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                  ScrollViewer.VerticalScrollBarVisibility="Auto"
                                  ScrollViewer.CanContentScroll="False"
                                  VirtualizingPanel.IsVirtualizing="False"
                                  Background="Transparent" BorderThickness="0"
                                  Visibility="{Binding UseTreeView, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TreeView.ItemContainerStyle>
                                <Style TargetType="TreeViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="0"/>
                                    <Setter Property="Margin" Value="0,0,0,1"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                                    <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="TreeViewItem">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="*"/>
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Expand/Collapse Button -->
                                                    <ToggleButton x:Name="Expander" Grid.Row="0" Grid.Column="0"
                                                                  IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}"
                                                                  ClickMode="Press" Width="20" Height="20" Margin="0,0,4,0"
                                                                  Background="Transparent" BorderThickness="0"
                                                                  VerticalAlignment="Center" HorizontalAlignment="Center">
                                                        <ToggleButton.Style>
                                                            <Style TargetType="ToggleButton">
                                                                <Setter Property="Template">
                                                                    <Setter.Value>
                                                                        <ControlTemplate TargetType="ToggleButton">
                                                                            <Border Background="Transparent" Width="16" Height="16">
                                                                                <TextBlock x:Name="ExpanderIcon" Text="▶" FontSize="10"
                                                                                           Foreground="{DynamicResource SecondaryText}"
                                                                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                                           RenderTransformOrigin="0.5,0.5">
                                                                                    <TextBlock.RenderTransform>
                                                                                        <RotateTransform x:Name="ExpanderRotation" Angle="0"/>
                                                                                    </TextBlock.RenderTransform>
                                                                                </TextBlock>
                                                                            </Border>
                                                                            <ControlTemplate.Triggers>
                                                                                <Trigger Property="IsChecked" Value="True">
                                                                                    <Trigger.EnterActions>
                                                                                        <BeginStoryboard>
                                                                                            <Storyboard>
                                                                                                <DoubleAnimation Storyboard.TargetName="ExpanderRotation"
                                                                                                                 Storyboard.TargetProperty="Angle"
                                                                                                                 To="90" Duration="0:0:0.2"/>
                                                                                            </Storyboard>
                                                                                        </BeginStoryboard>
                                                                                    </Trigger.EnterActions>
                                                                                    <Trigger.ExitActions>
                                                                                        <BeginStoryboard>
                                                                                            <Storyboard>
                                                                                                <DoubleAnimation Storyboard.TargetName="ExpanderRotation"
                                                                                                                 Storyboard.TargetProperty="Angle"
                                                                                                                 To="0" Duration="0:0:0.2"/>
                                                                                            </Storyboard>
                                                                                        </BeginStoryboard>
                                                                                    </Trigger.ExitActions>
                                                                                </Trigger>
                                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                                    <Setter TargetName="ExpanderIcon" Property="Foreground" Value="{DynamicResource PrimaryBlue}"/>
                                                                                </Trigger>
                                                                            </ControlTemplate.Triggers>
                                                                        </ControlTemplate>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </Style>
                                                        </ToggleButton.Style>
                                                    </ToggleButton>

                                                    <!-- Item Content -->
                                                    <Border Grid.Row="0" Grid.Column="1" Background="{TemplateBinding Background}"
                                                            CornerRadius="4" Padding="8,6" Margin="0,0,0,0">
                                                        <Border.Style>
                                                            <Style TargetType="Border">
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="{DynamicResource HoverBackground}"/>
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Border.Style>
                                                        <ContentPresenter ContentSource="Header"/>
                                                    </Border>

                                                    <!-- Children Container -->
                                                    <ItemsPresenter Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                                                    Margin="20,2,0,0"
                                                                    Visibility="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                </Grid>
                                                <ControlTemplate.Triggers>
                                                    <!-- Hide expander for items without children -->
                                                    <DataTrigger Binding="{Binding HasItems, RelativeSource={RelativeSource Self}}" Value="False">
                                                        <Setter TargetName="Expander" Property="Visibility" Value="Hidden"/>
                                                    </DataTrigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </TreeView.ItemContainerStyle>
                            <TreeView.Resources>
                                <HierarchicalDataTemplate DataType="{x:Type models:TreeViewItemModel}" ItemsSource="{Binding Children}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Checkbox with Three-State Support -->
                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                                  VerticalAlignment="Center" Margin="0,0,8,0" IsThreeState="False">
                                            <CheckBox.Style>
                                                <Style TargetType="CheckBox">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="CheckBox">
                                                                <Border Width="16" Height="16" Background="{DynamicResource CardBackground}"
                                                                        BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3">
                                                                    <Grid>
                                                                        <!-- Checked state only - no indeterminate state -->
                                                                        <TextBlock Text="✓" FontSize="10" Foreground="{DynamicResource PrimaryBlue}" FontWeight="Bold"
                                                                                   HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                                   Visibility="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                                    </Grid>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </CheckBox.Style>
                                        </CheckBox>

                                        <!-- Item Icon -->
                                        <TextBlock Grid.Column="1" Text="{Binding Icon, Mode=OneWay}"
                                                   FontSize="14" VerticalAlignment="Center" Margin="0,0,8,0" Foreground="{DynamicResource PrimaryBlue}"/>

                                        <!-- Item Name and Details -->
                                        <StackPanel Grid.Column="2" Orientation="Vertical" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" FontSize="13" FontWeight="SemiBold"
                                                       TextTrimming="CharacterEllipsis" Foreground="{DynamicResource PrimaryText}"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                <TextBlock Text="{Binding ImageCount, Mode=OneWay}" FontSize="11" Foreground="{DynamicResource SecondaryText}"/>
                                                <TextBlock Text=" images" FontSize="11" Foreground="{DynamicResource SecondaryText}"/>
                                                <TextBlock Text=" • " FontSize="11" Foreground="{DynamicResource SecondaryText}" Margin="4,0"/>
                                                <TextBlock Text="{Binding EstimatedSize, Mode=OneWay}" FontSize="11" Foreground="{DynamicResource SecondaryText}"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- Privacy Icon (for albums only) -->
                                        <TextBlock Grid.Column="3" Text="{Binding Album.PrivacyIcon}" FontSize="12"
                                                   VerticalAlignment="Center" Margin="8,0"
                                                   ToolTip="{Binding Album.PrivacyStatus}" Foreground="{DynamicResource SecondaryText}"
                                                   Visibility="{Binding IsAlbum, Mode=OneWay, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                        <!-- Selection Status (for albums only) -->
                                        <Border Grid.Column="4" Width="16" Height="16" Background="{DynamicResource PrimaryBlue}" CornerRadius="8"
                                                VerticalAlignment="Center" Margin="4,0,0,0">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsAlbum}" Value="True">
                                                            <Setter Property="Visibility" Value="{Binding IsChecked, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="✓" FontSize="10" Foreground="White" FontWeight="Bold"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                </HierarchicalDataTemplate>
                            </TreeView.Resources>
                        </TreeView>

                        <!-- List View (when UseTreeView is false) -->
                        <ListView ItemsSource="{Binding AlbumsView}"
                                  ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                  ScrollViewer.VerticalScrollBarVisibility="Auto"
                                  ScrollViewer.CanContentScroll="False"
                                  VirtualizingPanel.IsVirtualizing="False"
                                  Background="Transparent" BorderThickness="0"
                                  Visibility="{Binding UseTreeView, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="0"/>
                                    <Setter Property="Margin" Value="0,0,0,2"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Background" Value="{DynamicResource CardBackground}"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ListViewItem">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="4" Padding="12,8">
                                                    <Border.Effect>
                                                        <DropShadowEffect Color="{Binding DataContext.IsDarkTheme, RelativeSource={RelativeSource AncestorType=Window}, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                                                          Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                    </Border.Effect>
                                                    <ContentPresenter/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ListView.ItemContainerStyle>
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Checkbox -->
                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                  VerticalAlignment="Center" Margin="0,0,12,0">
                                            <CheckBox.Style>
                                                <Style TargetType="CheckBox">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="CheckBox">
                                                                <Border Width="16" Height="16" Background="{DynamicResource DarkBackground}"
                                                                        BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3">
                                                                    <TextBlock Text="✓" FontSize="12" Foreground="{DynamicResource PrimaryBlue}" FontWeight="Bold"
                                                                               HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                               Visibility="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </CheckBox.Style>
                                        </CheckBox>

                                        <!-- Album Icon -->
                                        <TextBlock Grid.Column="1" Text="{Binding AlbumTypeIcon}"
                                                   FontSize="12" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="{DynamicResource PrimaryBlue}"/>

                                        <!-- Album Name -->
                                        <TextBlock Grid.Column="2" Text="{Binding DisplayName}"
                                                   FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center"
                                                   TextTrimming="CharacterEllipsis" Foreground="{DynamicResource PrimaryText}"/>

                                        <!-- Image Count -->
                                        <TextBlock Grid.Column="3" VerticalAlignment="Center" Margin="12,0">
                                            <Run Text="{Binding ImageCount, Mode=OneWay}" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                                            <Run Text=" images" FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                                        </TextBlock>

                                        <!-- Size -->
                                        <TextBlock Grid.Column="4" Text="{Binding EstimatedSize, Mode=OneWay}"
                                                   FontSize="12" Foreground="{DynamicResource SecondaryText}" VerticalAlignment="Center" Margin="12,0"/>

                                        <!-- Privacy & Selection Status -->
                                        <StackPanel Grid.Column="5" VerticalAlignment="Center" Orientation="Horizontal" Margin="12,0,0,0">
                                            <TextBlock Text="{Binding PrivacyIcon}" FontSize="12" Margin="0,0,6,0"
                                                       ToolTip="{Binding PrivacyStatus}" Foreground="{DynamicResource SecondaryText}"/>
                                            <Border Width="20" Height="20" Background="{DynamicResource PrimaryBlue}" CornerRadius="10"
                                                    Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <TextBlock Text="✓" FontSize="12" Foreground="White" FontWeight="Bold"
                                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- Download Configuration Section with Dynamic Theme -->
        <Border Grid.Row="3" Background="{DynamicResource CardBackground}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                CornerRadius="8" Padding="24"
                Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border.Effect>
                <DropShadowEffect Color="{Binding IsDarkTheme, Converter={x:Static local:ThemeColorConverter.Instance}}"
                                  Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.5"/>
            </Border.Effect>
            <StackPanel>
                <!-- Target Directory Section -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBlock Text="Target Directory:" VerticalAlignment="Center" Width="140" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryText}"/>
                    <TextBox Text="{Binding TargetDirectory}"
                             IsReadOnly="True"
                             Width="350" Margin="0,0,12,0" Padding="12,8" FontSize="14"
                             BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Background="{DynamicResource DarkBackground}" Foreground="{DynamicResource PrimaryText}"/>
                    <Button Content="Browse..."
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Padding="12,6" FontSize="14" FontWeight="SemiBold" Margin="0,0,12,0"
                            Background="Transparent" Foreground="{DynamicResource PrimaryBlue}" BorderThickness="1" BorderBrush="{DynamicResource PrimaryBlue}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Start Download Button (when not downloading) -->
                    <Button Content="Start Download" FontSize="14" Padding="12,6" FontWeight="SemiBold"
                            Command="{Binding StartDownloadCommand}"
                            IsEnabled="{Binding CanStartDownload}"
                            Background="{DynamicResource PrimaryBlue}" Foreground="White" BorderThickness="0"
                            Visibility="{Binding IsDownloading, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Preparation Status (when preparing) -->
                    <StackPanel Orientation="Vertical" Margin="12,0,0,0" VerticalAlignment="Center"
                                Visibility="{Binding IsPreparingDownload, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding PreparationStatus}" FontSize="12" FontWeight="SemiBold"
                                   Foreground="{DynamicResource PrimaryBlue}" Margin="0,0,0,4"/>
                        <ProgressBar Width="200" Height="6" IsIndeterminate="True"
                                     Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}"/>
                    </StackPanel>

                    <!-- Download Progress (when downloading) - Next to buttons -->
                    <StackPanel Orientation="Vertical" Margin="12,0,0,0" VerticalAlignment="Center"
                                Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding ProgressText}" FontSize="12" FontWeight="SemiBold"
                                   Foreground="{DynamicResource PrimaryText}" Margin="0,0,0,4"/>
                        <ProgressBar Width="200" Height="6" Value="{Binding OverallProgress}" Maximum="100"
                                     Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}"/>
                    </StackPanel>
                </StackPanel>

                <!-- Download Progress Section (when downloading) -->
                <StackPanel HorizontalAlignment="Center" Margin="0,20,0,0"
                            Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding ProgressText}" FontSize="16" FontWeight="SemiBold"
                               HorizontalAlignment="Center" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryText}"/>
                    <ProgressBar Width="400" Height="8" Value="{Binding OverallProgress}" Maximum="100"
                                 Background="{DynamicResource ProgressBackground}" Foreground="{DynamicResource PrimaryBlue}" Margin="0,0,0,8"/>

                    <!-- Download Speed and Time Stats -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                        <TextBlock Text="Speed: " FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding DownloadSpeed}" FontSize="12" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}" Margin="0,0,16,0"/>
                        <TextBlock Text="ETA: " FontSize="12" Foreground="{DynamicResource SecondaryText}"/>
                        <TextBlock Text="{Binding EstimatedTimeRemaining}" FontSize="12" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryBlue}"/>
                    </StackPanel>

                    <!-- Control Buttons (when downloading) -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="{Binding PauseResumeButtonText}" FontSize="14" Padding="12,6" Margin="0,0,8,0" FontWeight="SemiBold"
                                Command="{Binding PauseResumeDownloadCommand}"
                                IsEnabled="{Binding CanPauseDownload}"
                                Background="#FFA500" Foreground="White" BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <Button Content="Cancel" FontSize="14" Padding="12,6" FontWeight="SemiBold"
                                Command="{Binding CancelDownloadCommand}"
                                IsEnabled="{Binding CanCancelDownload}"
                                Background="Transparent" Foreground="#DC3545" BorderThickness="1" BorderBrush="#DC3545">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Footer Section -->
        <Border Grid.Row="4" Margin="0,16,0,0" Padding="16,12"
                Background="Transparent" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="© 2025 WinMug" FontSize="12" Foreground="{DynamicResource SecondaryText}" VerticalAlignment="Center"/>
                <TextBlock Text=" • " FontSize="12" Foreground="{DynamicResource SecondaryText}" Margin="8,0" VerticalAlignment="Center"/>
                <Button Content="User Guide"
                        Command="{Binding OpenUserGuideCommand}"
                        FontSize="12" Foreground="{DynamicResource PrimaryBlue}"
                        Background="Transparent" BorderThickness="0"
                        Padding="0" Margin="0" Cursor="Hand"
                        ToolTip="Open WinMug User Guide">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <ContentPresenter/>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                <TextBlock Text=" • " FontSize="12" Foreground="{DynamicResource SecondaryText}" Margin="8,0" VerticalAlignment="Center"/>
                <Button Content="GitHub"
                        Command="{Binding OpenGitHubCommand}"
                        FontSize="12" Foreground="{DynamicResource PrimaryBlue}"
                        Background="Transparent" BorderThickness="0"
                        Padding="0" Margin="0" Cursor="Hand"
                        ToolTip="Visit WinMug on GitHub">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <ContentPresenter/>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
