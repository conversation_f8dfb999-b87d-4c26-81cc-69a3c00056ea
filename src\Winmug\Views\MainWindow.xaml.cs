using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Winmug.ViewModels;

namespace Winmug.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainWindowViewModel _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        try
        {
            _viewModel = viewModel;
            InitializeComponent();
            DataContext = viewModel;

            // Initialize the view model with error handling
            Loaded += OnWindowLoaded;

            // Subscribe to property changes for window resizing
            _viewModel.PropertyChanged += OnViewModelPropertyChanged;

            // Add mouse wheel event handling for better scrolling
            PreviewMouseWheel += OnPreviewMouseWheel;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing main window: {ex.Message}\n\nDetails: {ex}",
                "Window Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
        }
    }

    private async void OnWindowLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            await _viewModel.InitializeAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error during window initialization: {ex.Message}\n\nDetails: {ex}",
                "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnViewModelPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // Handle window resizing when content visibility changes
        if (e.PropertyName == nameof(_viewModel.IsFolderStructureLoaded) ||
            e.PropertyName == nameof(_viewModel.IsAuthenticated))
        {
            Dispatcher.BeginInvoke(() =>
            {
                // Reset SizeToContent to allow automatic resizing
                SizeToContent = SizeToContent.Manual;
                SizeToContent = SizeToContent.Height;

                // Ensure minimum size constraints
                if (ActualHeight < MinHeight)
                {
                    Height = MinHeight;
                }
                if (ActualWidth < MinWidth)
                {
                    Width = MinWidth;
                }
            });
        }
    }

    private void OnPreviewMouseWheel(object sender, MouseWheelEventArgs e)
    {
        // Find the ScrollViewer in the albums section
        var scrollViewer = FindScrollViewer(this);
        if (scrollViewer != null)
        {
            // Check if mouse is over the albums area
            var mousePosition = e.GetPosition(this);
            var albumsArea = FindName("AlbumsScrollViewer") as ScrollViewer;

            if (albumsArea != null)
            {
                var albumsPosition = albumsArea.TransformToAncestor(this).Transform(new Point(0, 0));
                var albumsBounds = new Rect(albumsPosition.X, albumsPosition.Y, albumsArea.ActualWidth, albumsArea.ActualHeight);

                if (albumsBounds.Contains(mousePosition))
                {
                    // Scroll the albums ScrollViewer
                    if (e.Delta > 0)
                        albumsArea.LineUp();
                    else
                        albumsArea.LineDown();

                    e.Handled = true;
                }
            }
        }
    }

    private ScrollViewer? FindScrollViewer(DependencyObject parent)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            if (child is ScrollViewer scrollViewer)
                return scrollViewer;

            var result = FindScrollViewer(child);
            if (result != null)
                return result;
        }
        return null;
    }

    private void TreeView_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
    {
        // Forward mouse wheel events to the parent ScrollViewer
        var scrollViewer = FindName("AlbumsScrollViewer") as ScrollViewer;
        if (scrollViewer != null)
        {
            if (e.Delta > 0)
            {
                scrollViewer.LineUp();
                scrollViewer.LineUp();
                scrollViewer.LineUp(); // Scroll multiple lines for better responsiveness
            }
            else
            {
                scrollViewer.LineDown();
                scrollViewer.LineDown();
                scrollViewer.LineDown(); // Scroll multiple lines for better responsiveness
            }
            e.Handled = true;
        }
    }
}
